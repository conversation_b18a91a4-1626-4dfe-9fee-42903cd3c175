// src/hooks/PermissionsContext.tsx
import React, { createContext, ReactNode, useContext } from "react";
import { usePermissions } from "./usePermissions";

interface LocationData {
  lat: number;
  lng: number;
  address?: string;
}

interface PermissionsContextType {
  // Camera & Microphone
  hasCameraPermission: boolean;
  hasMicrophonePermission: boolean;
  requestCameraPermission: () => Promise<boolean>;
  requestMicrophonePermission: () => Promise<boolean>;

  // Location
  hasLocationPermission: boolean;
  location: LocationData | null;
  isLoadingLocation: boolean;
  getCurrentLocation: () => Promise<LocationData | null>;
  getAddressFromCoords: (lat: number, lng: number) => Promise<string>;

  // Media Library
  hasMediaLibraryPermission: boolean;
  requestMediaLibraryPermission: () => Promise<boolean>;
  getMediaLibraryPermission: () => Promise<boolean>;

  // Combined actions
  requestAllPermissions: () => Promise<{
    camera: boolean;
    microphone: boolean;
    location: boolean;
    mediaLibrary: boolean;
  }>;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(
  undefined
);

export function PermissionsProvider({ children }: { children: ReactNode }) {
  const permissions = usePermissions();

  return (
    <PermissionsContext.Provider value={permissions}>
      {children}
    </PermissionsContext.Provider>
  );
}

export function usePermissionsContext() {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error(
      "usePermissionsContext must be used within a PermissionsProvider"
    );
  }
  return context;
}
