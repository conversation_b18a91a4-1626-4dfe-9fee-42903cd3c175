import * as Location from "expo-location";
import * as MediaLibrary from "expo-media-library";
import { useEffect, useState } from "react";
import {
  useCameraPermission,
  useMicrophonePermission,
} from "react-native-vision-camera";

interface LocationData {
  lat: number;
  lng: number;
  address?: string;
}

export function usePermissions() {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  // Built-in hooks
  const {
    hasPermission: hasMicrophonePermission,
    requestPermission: requestMicrophonePermission,
  } = useMicrophonePermission();

  const {
    hasPermission: hasCameraPermission,
    requestPermission: requestCameraPermission,
  } = useCameraPermission();

  // Use Expo's built-in media library permissions hook
  const [mediaLibraryPermissionResponse, requestMediaLibraryPermissionRaw] =
    MediaLibrary.usePermissions();

  // Get address from coordinates
  async function getAddressFromCoords(
    lat: number,
    lng: number
  ): Promise<string> {
    try {
      const [result] = await Location.reverseGeocodeAsync({
        latitude: lat,
        longitude: lng,
      });

      if (result) {
        const addressParts = [
          result.name,
          result.street,
          result.city,
          result.region,
          result.country,
        ].filter(Boolean);

        return addressParts.join(", ");
      }
      return "Address not found";
    } catch (error) {
      console.error("Error getting address:", error);
      return "Unable to fetch address";
    }
  }

  // Get current location with address
  async function getCurrentLocation(): Promise<LocationData | null> {
    try {
      setIsLoadingLocation(true);
      const { status } = await Location.getForegroundPermissionsAsync();

      if (status !== "granted") {
        console.log("Location permission not granted");
        return null;
      }

      const currentLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const lat = currentLocation.coords.latitude;
      const lng = currentLocation.coords.longitude;
      const address = await getAddressFromCoords(lat, lng);

      const locationData = { lat, lng, address };
      setLocation(locationData);
      return locationData;
    } catch (error) {
      console.error("Error getting location:", error);
      return null;
    } finally {
      setIsLoadingLocation(false);
    }
  }

  // Request all permissions
  async function requestAllPermissions() {
    const cameraResult = await requestCameraPermission();
    const micResult = await requestMicrophonePermission();
    const locationResult = await Location.requestForegroundPermissionsAsync();

    // Request media library permission using the hook
    const mediaLibraryResult = await requestMediaLibraryPermission();

    setHasLocationPermission(locationResult.status === "granted");

    // Auto-fetch location if permission granted
    if (locationResult.status === "granted") {
      await getCurrentLocation();
    }

    return {
      camera: cameraResult,
      microphone: micResult,
      location: locationResult.status === "granted",
      mediaLibrary: mediaLibraryResult,
    };
  }

  // Check location permission on mount
  useEffect(() => {
    (async () => {
      const { status } = await Location.getForegroundPermissionsAsync();
      setHasLocationPermission(status === "granted");

      // Auto-fetch location if already granted
      if (status === "granted") {
        getCurrentLocation();
      }
    })();
  }, []);

  // Request media library permission and return boolean
  async function requestMediaLibraryPermission(): Promise<boolean> {
    try {
      const result = await requestMediaLibraryPermissionRaw();
      return result.granted;
    } catch (error) {
      console.error("Error requesting media library permission:", error);
      return false;
    }
  }

  // Get media library permission status
  async function getMediaLibraryPermission(): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.getPermissionsAsync();
      return status === "granted";
    } catch (error) {
      console.error("Error getting media library permission:", error);
      return false;
    }
  }

  return {
    // Camera & Microphone
    hasCameraPermission,
    hasMicrophonePermission,
    requestCameraPermission,
    requestMicrophonePermission,

    // Location
    hasLocationPermission,
    location,
    isLoadingLocation,

    // Media Library
    hasMediaLibraryPermission: mediaLibraryPermissionResponse?.granted || false,
    requestMediaLibraryPermission,
    getMediaLibraryPermission,

    // Combined actions
    requestAllPermissions,
    getCurrentLocation,
    getAddressFromCoords,
  };
}
