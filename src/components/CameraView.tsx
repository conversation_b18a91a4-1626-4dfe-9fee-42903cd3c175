import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import * as MediaLibrary from "expo-media-library";
import React, { useEffect, useRef, useState } from "react";
import {
  Alert,
  Animated,
  Pressable,
  StyleSheet,
  Text,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Camera, useCameraDevice, VideoFile } from "react-native-vision-camera";
import { usePermissionsContext } from "../hooks/PermissionsContext";
import { useAppSettings } from "../hooks/useAppSettings";

interface CameraViewProps {
  onVideoRecorded?: (video: VideoFile) => void;
}

export default function CameraView({ onVideoRecorded }: CameraViewProps) {
  const device = useCameraDevice("back");
  const cameraRef = useRef<Camera>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [currentTime, setCurrentTime] = useState(new Date());
  const insets = useSafeAreaInsets();

  const {
    location,
    hasLocationPermission,
    hasMediaLibraryPermission,
    requestMediaLibraryPermission,
  } = usePermissionsContext();
  const { settings, updateSettings } = useAppSettings();

  // Animation for recording indicator
  const recordingOpacity = useRef(new Animated.Value(0)).current;

  // Timer for recording duration and current time
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      // Start recording animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(recordingOpacity, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(recordingOpacity, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Recording timer
      interval = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else {
      recordingOpacity.setValue(0);
      setRecordingTime(0);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, recordingOpacity]);

  // Update current time every second
  useEffect(() => {
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timeInterval);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  const getCurrentTimestamp = () => {
    const options: Intl.DateTimeFormatOptions = {
      timeZone: settings.timezone || "UTC",
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: settings.timestampFormat === "12h",
    };
    return currentTime.toLocaleString(settings.dateFormat || "en-US", options);
  };

  const getLocationText = () => {
    if (!settings.locationTagging || !location) return "";
    if (settings.locationFormat === "coordinates") {
      return `${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}`;
    }
    return location.address || "Location unavailable";
  };

  const getVideoResolution = () => {
    switch (settings.videoResolution) {
      case "720p":
        return { width: 1280, height: 720 };
      case "1080p":
        return { width: 1920, height: 1080 };
      case "4k":
        return { width: 3840, height: 2160 };
      default:
        return "auto";
    }
  };

  const getVideoBitRate = () => {
    switch (settings.videoResolution) {
      case "720p":
        return "normal";
      case "1080p":
        return "high";
      case "4k":
        return "high";
      default:
        return "normal";
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current || !device) {
      Alert.alert("Error", "Camera not available");
      return;
    }

    // Check media library permission before recording
    if (!hasMediaLibraryPermission) {
      const granted = await requestMediaLibraryPermission();
      if (!granted) {
        Alert.alert(
          "Permission Required",
          "Media library access is required to save videos. Please enable it in settings."
        );
        return;
      }
    }

    try {
      setIsRecording(true);

      const videoConfig: any = {
        flash: "off",
        videoCodec: "h264",
        videoBitRate: getVideoBitRate(),
        onRecordingFinished: async (video: VideoFile) => {
          console.log("Recording finished:", video.path);
          setIsRecording(false);
          await saveVideoToGallery(video);
          onVideoRecorded?.(video);
        },
        onRecordingError: (error: any) => {
          console.error("Recording error:", error);
          setIsRecording(false);
          Alert.alert(
            "Recording Error",
            error.message || "Failed to record video"
          );
        },
      };

      // Add resolution if not auto
      const resolution = getVideoResolution();
      if (resolution !== "auto") {
        videoConfig.videoSize = resolution;
      }

      await cameraRef.current.startRecording(videoConfig);
    } catch (error: any) {
      setIsRecording(false);
      console.error("Failed to start recording:", error);
      Alert.alert("Error", error.message || "Failed to start recording");
    }
  };

  const stopRecording = async () => {
    if (!cameraRef.current || !isRecording) return;

    try {
      await cameraRef.current.stopRecording();
    } catch (error: any) {
      console.error("Failed to stop recording:", error);
      Alert.alert("Error", error.message || "Failed to stop recording");
      setIsRecording(false);
    }
  };

  const saveVideoToGallery = async (video: VideoFile) => {
    try {
      console.log("Saving video to gallery:", video.path);

      // Create asset from the video file
      const asset = await MediaLibrary.createAssetAsync(video.path);
      console.log("Asset created:", asset.id);

      // Check if album exists
      const albumName = "CameraApp";
      let album = await MediaLibrary.getAlbumAsync(albumName);

      if (album) {
        // Album exists, add asset to it
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
        console.log("Video added to existing album");
      } else {
        // Album doesn't exist, create it with the asset
        album = await MediaLibrary.createAlbumAsync(albumName, asset, false);
        console.log("New album created with video");
      }

      Alert.alert("Success", "Video saved to gallery");
    } catch (error: any) {
      console.error("Failed to save video:", error);
      Alert.alert(
        "Save Error",
        error.message || "Failed to save video to gallery"
      );
    }
  };

  const cycleResolution = () => {
    const resolutions = ["720p", "1080p", "4k", "auto"];
    const currentIndex = resolutions.indexOf(settings.videoResolution);
    const nextIndex = (currentIndex + 1) % resolutions.length;
    const newResolution = resolutions[nextIndex] as any;
    updateSettings({ videoResolution: newResolution });

    // Show feedback
    Alert.alert(
      "Resolution Changed",
      `Video resolution set to ${newResolution.toUpperCase()}`
    );
  };

  const handleRecordPress = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  if (!device) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="camera-alt" size={64} color="#666" />
          <Text style={styles.errorText}>No camera device available</Text>
          <Text style={styles.errorSubtext}>
            Please check camera permissions
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Camera
        ref={cameraRef}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        video={true}
        audio={true}
        enableLocation={settings.locationTagging && hasLocationPermission}
        photoQualityBalance="balanced"
      />

      {/* Video Overlay with Timestamp and Location */}
      <View style={[styles.overlayContainer, { bottom: insets.bottom + 140 }]}>
        <View style={styles.timestampOverlay}>
          {/* Recording indicator */}
          {isRecording && (
            <View style={styles.recordingIndicator}>
              <Animated.View
                style={[styles.recordingDot, { opacity: recordingOpacity }]}
              />
              <Text style={styles.recordingText}>REC</Text>
              <Text style={styles.recordingTime}>
                {formatTime(recordingTime)}
              </Text>
            </View>
          )}

          {/* Timestamp */}
          <Text style={styles.timestampText}>{getCurrentTimestamp()}</Text>

          {/* Location */}
          {settings.locationTagging && location && (
            <Text style={styles.locationText}>{getLocationText()}</Text>
          )}
        </View>
      </View>

      {/* Top Controls */}
      <View style={[styles.topControls, { top: insets.top + 80 }]}>
        {/* Resolution Display */}
        <Pressable onPress={cycleResolution} style={styles.resolutionButton}>
          <Text style={styles.resolutionText}>
            [{settings.videoResolution.toUpperCase()}]
          </Text>
        </Pressable>

        {/* Location Badge */}
        <View
          style={[
            styles.locationBadge,
            { backgroundColor: settings.locationTagging ? "#4CAF50" : "#666" },
          ]}
        >
          <MaterialIcons
            name={settings.locationTagging ? "location-on" : "location-off"}
            size={16}
            color="white"
          />
          <Text style={styles.locationBadgeText}>
            {settings.locationTagging ? "ON" : "OFF"}
          </Text>
        </View>
      </View>

      {/* Bottom Controls */}
      <View style={[styles.bottomControls, { bottom: insets.bottom + 30 }]}>
        {/* Record Button */}
        <Pressable
          style={[
            styles.recordButton,
            {
              backgroundColor: isRecording ? "#FF0000" : "#FFFFFF",
              borderColor: isRecording ? "#FFFFFF" : "#FF0000",
            },
          ]}
          onPress={handleRecordPress}
          disabled={!device}
        >
          <View
            style={[
              styles.recordButtonInner,
              {
                backgroundColor: isRecording ? "#FFFFFF" : "#FF0000",
                borderRadius: isRecording ? 8 : 35,
                width: isRecording ? 35 : 40,
                height: isRecording ? 35 : 40,
              },
            ]}
          />
        </Pressable>

        {/* Recording Status Text */}
        {isRecording && (
          <Text style={styles.recordingStatusText}>
            Recording • {formatTime(recordingTime)}
          </Text>
        )}
      </View>

      {/* Help Text */}
      {!isRecording && (
        <View style={[styles.helpContainer, { bottom: insets.bottom + 120 }]}>
          <Text style={styles.helpText}>Tap to start recording</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorText: {
    color: "white",
    fontSize: 18,
    fontWeight: "600",
    marginTop: 16,
    textAlign: "center",
  },
  errorSubtext: {
    color: "#888",
    fontSize: 14,
    marginTop: 8,
    textAlign: "center",
  },
  overlayContainer: {
    position: "absolute",
    left: 20,
    right: 20,
    alignItems: "center",
  },
  timestampOverlay: {
    backgroundColor: "rgba(0,0,0,0.8)",
    padding: 16,
    borderRadius: 12,
    alignItems: "center",
    minWidth: 200,
  },
  recordingIndicator: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#FF0000",
    marginRight: 8,
  },
  recordingText: {
    color: "#FF0000",
    fontSize: 14,
    fontWeight: "bold",
    marginRight: 8,
  },
  recordingTime: {
    color: "#FF0000",
    fontSize: 14,
    fontWeight: "bold",
  },
  timestampText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  locationText: {
    color: "#4CAF50",
    fontSize: 12,
    marginTop: 6,
    textAlign: "center",
  },
  topControls: {
    position: "absolute",
    left: 20,
    right: 20,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  resolutionButton: {
    backgroundColor: "rgba(0,0,0,0.8)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  resolutionText: {
    color: "white",
    fontSize: 12,
    fontWeight: "600",
  },
  locationBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  locationBadgeText: {
    color: "white",
    fontSize: 11,
    marginLeft: 4,
    fontWeight: "600",
  },
  bottomControls: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 4,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  recordButtonInner: {
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
  },
  recordingStatusText: {
    color: "#FF0000",
    fontSize: 14,
    fontWeight: "600",
    marginTop: 12,
    textAlign: "center",
  },
  helpContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    alignItems: "center",
  },
  helpText: {
    color: "rgba(255,255,255,0.7)",
    fontSize: 14,
    textAlign: "center",
  },
});
