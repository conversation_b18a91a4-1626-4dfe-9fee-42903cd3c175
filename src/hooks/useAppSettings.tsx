import AsyncStorage from "@react-native-async-storage/async-storage";
import { useEffect, useState } from "react";

export interface AppSettings {
  theme: "system" | "light" | "dark";
  videoResolution: "720p" | "1080p" | "4k" | "auto";
  timestampFormat: "12h" | "24h";
  dateFormat: string;
  timezone: string;
  locationTagging: boolean;
  locationFormat: "coordinates" | "address";
  autoDeleteDays: number | null;
}

const DEFAULT_SETTINGS: AppSettings = {
  theme: "system",
  videoResolution: "1080p",
  timestampFormat: "24h",
  dateFormat: "en-US",
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  locationTagging: true,
  locationFormat: "address",
  autoDeleteDays: null,
};

const SETTINGS_KEY = "@camera_app_settings";

export function useAppSettings() {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const storedSettings = await AsyncStorage.getItem(SETTINGS_KEY);
      if (storedSettings) {
        setSettings({ ...DEFAULT_SETTINGS, ...JSON.parse(storedSettings) });
      }
    } catch (error) {
      console.error("Failed to load settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);
      await AsyncStorage.setItem(SETTINGS_KEY, JSON.stringify(updatedSettings));
    } catch (error) {
      console.error("Failed to save settings:", error);
    }
  };

  return {
    settings,
    updateSettings,
    isLoading,
  };
}
