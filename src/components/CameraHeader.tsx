import Feather from "@expo/vector-icons/Feather";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useRouter } from "expo-router";
import React from "react";
import { ActivityIndicator, Pressable, Text, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { usePermissionsContext } from "../hooks/PermissionsContext";

export default function CameraHeader() {
  const insets = useSafeAreaInsets();
  const { push } = useRouter();

  const {
    location,
    isLoadingLocation,
    hasLocationPermission,
    requestAllPermissions,
    getCurrentLocation,
  } = usePermissionsContext();

  function navigateToSetting() {
    push("/setting");
  }

  // Toggle location on/off
  async function handleLocationToggle() {
    console.log("🔵 Location button clicked");
    console.log("📍 Current location:", location);
    console.log("✅ Has permission:", hasLocationPermission);

    try {
      if (hasLocationPermission) {
        console.log("🔄 Fetching location...");
        const result = await getCurrentLocation();
        console.log("✅ Location fetched:", result);
      } else {
        console.log("🔒 Requesting permissions...");
        const result = await requestAllPermissions();
        console.log("✅ Permissions granted:", result);
      }
    } catch (error) {
      console.error("❌ Error in handleLocationToggle:", error);
    }
  }

  return (
    <View
      style={{
        position: "absolute",
        top: insets.top + 4,
        flexDirection: "row",
        justifyContent: "space-between",
        width: "100%",
        paddingHorizontal: 12,
        alignItems: "center",
        zIndex: 10,
      }}
    >
      <Pressable
        onPress={navigateToSetting}
        style={{
          padding: 12,
          backgroundColor: "#00000085",
          borderRadius: 100,
        }}
      >
        <Feather name="settings" size={24} color="white" />
      </Pressable>

      <Text style={{ color: "#fff", fontSize: 12 }}>[ 1080P ]</Text>

      <Pressable
        onPress={handleLocationToggle}
        style={{
          padding: 12,
          backgroundColor: "#00000085",
          borderRadius: 100,
        }}
      >
        {isLoadingLocation ? (
          <ActivityIndicator size="small" color="#fff" />
        ) : (
          <MaterialIcons
            name={location ? "location-on" : "location-off"}
            size={24}
            color={location ? "#4CAF50" : "#fff"}
          />
        )}
      </Pressable>
    </View>
  );
}
