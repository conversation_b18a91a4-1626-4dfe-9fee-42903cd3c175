// app/index.tsx
import CameraHeader from "@/src/components/CameraHeader";
import CameraView from "@/src/components/CameraView";
import { usePermissionsContext } from "@/src/hooks/PermissionsContext";
import { useAppSettings } from "@/src/hooks/useAppSettings";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { useEffect } from "react";
import { ActivityIndicator, Pressable, Text, View } from "react-native";

export default function Index() {
  const {
    hasCameraPermission,
    hasMicrophonePermission,
    requestAllPermissions,
  } = usePermissionsContext();

  const { isLoading } = useAppSettings();

  useEffect(() => {
    requestAllPermissions();
  }, []);

  if (isLoading) {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
          backgroundColor: "#000",
        }}
      >
        <ActivityIndicator size="large" color="#4CAF50" />
      </View>
    );
  }

  if (!hasCameraPermission || !hasMicrophonePermission) {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
          backgroundColor: "#000",
        }}
      >
        <MaterialIcons name="camera-alt" size={64} color="#666" />
        <Text
          style={{
            color: "white",
            fontSize: 18,
            marginTop: 16,
            textAlign: "center",
          }}
        >
          Camera permissions required
        </Text>
        <Text
          style={{
            color: "#666",
            fontSize: 14,
            marginTop: 8,
            textAlign: "center",
            paddingHorizontal: 20,
          }}
        >
          This app needs camera and microphone access to record videos
        </Text>
        <Pressable
          onPress={requestAllPermissions}
          style={{
            backgroundColor: "#4CAF50",
            paddingHorizontal: 24,
            paddingVertical: 12,
            borderRadius: 8,
            marginTop: 20,
          }}
        >
          <Text style={{ color: "white", fontWeight: "600" }}>
            Grant Permissions
          </Text>
        </Pressable>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: "#000" }}>
      <CameraHeader />
      <CameraView />
    </View>
  );
}
